import React, { useCallback, useEffect, useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useHistory } from 'react-router-dom';
import { push } from 'connected-react-router';
import { withTranslation, Trans } from 'react-i18next';
import moment from 'moment-timezone';
import compact from 'lodash/compact';
import each from 'lodash/each';
import find from 'lodash/find';
import flatten from 'lodash/flatten';
import get from 'lodash/get';
import includes from 'lodash/includes';
import isEqual from 'lodash/isEqual';
import isEmpty from 'lodash/isEmpty';
import isFinite from 'lodash/isFinite';
import keys from 'lodash/keys';
import keyBy from 'lodash/keyBy';
import map from 'lodash/map';
import reject from 'lodash/reject';
import values from 'lodash/values';
import isNil from 'lodash/isNil';
import { Box, Flex, Text } from 'theme-ui';
import ArrowUpwardIcon from '@material-ui/icons/ArrowUpward';
import ErrorRoundedIcon from '@material-ui/icons/ErrorRounded';
import MoreVertRoundedIcon from '@material-ui/icons/MoreVertRounded';
import KeyboardArrowDownRoundedIcon from '@material-ui/icons/KeyboardArrowDownRounded';
import EditIcon from '@material-ui/icons/EditRounded';
import { components as vizComponents, utils as vizUtils } from '@tidepool/viz';
const { GLYCEMIC_RANGE } = vizUtils.constants;
import ScrollToTop from 'react-scroll-to-top';
import styled from '@emotion/styled';
import { useFlags, useLDClient } from 'launchdarkly-react-client-sdk';

import {
  bindPopover,
  bindTrigger,
  usePopupState,
} from 'material-ui-popup-state/hooks';

import {
  MediumTitle,
  Title,
  Body1,
} from '../../components/elements/FontStyles';

import Button from '../../components/elements/Button';
import HoverButton from '../../components/elements/HoverButton';
import Table from '../../components/elements/Table';
import { TagList } from '../../components/elements/Tag';
import PatientForm from '../../components/clinic/PatientForm';
import TideDashboardConfigForm, { validateTideConfig } from '../../components/clinic/TideDashboardConfigForm';
import BgSummaryCell from '../../components/clinic/BgSummaryCell';
import DataConnectionsModal from '../../components/datasources/DataConnectionsModal';
import Popover from '../../components/elements/Popover';
import PopoverMenu from '../../components/elements/PopoverMenu';
import RadioGroup from '../../components/elements/RadioGroup';
import DeltaBar from '../../components/elements/DeltaBar';
import Pill from '../../components/elements/Pill';
import PatientDrawer, { isValidAgpPeriod } from './PatientDrawer';
import utils from '../../core/utils';

import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '../../components/elements/Dialog';

import * as actions from '../../redux/actions';
import { useToasts } from '../../providers/ToastProvider';
import { useIsFirstRender, useLocalStorage, usePrevious } from '../../core/hooks';
import { fieldsAreValid } from '../../core/forms';

import {
  patientSchema as validationSchema,
  tideDashboardConfigSchema,
  lastDataFilterOptions,
  summaryPeriodOptions,
} from '../../core/clinicUtils';

import { MGDL_UNITS, MMOLL_UNITS } from '../../core/constants';
import DataInIcon from '../../core/icons/DataInIcon.svg';
import { colors, fontWeights, radii } from '../../themes/baseTheme';
import PatientLastReviewed from '../../components/clinic/PatientLastReviewed';

const { Loader } = vizComponents;
const { formatBgValue } = vizUtils.bg;
const { formatStatsPercentage } = vizUtils.stat;

const {
  formatDateRange,
  getLocalizedCeiling,
  getOffset,
  getTimezoneFromTimePrefs
} = vizUtils.datetime;

const StyledScrollToTop = styled(ScrollToTop)`
  background-color: ${colors.purpleMedium};
  right: 20px;
  bottom: 70px;
  border-radius: 20px;
  padding-top: 4px;
`;

const CATEGORY = {
  timeInVeryLowPercent: 'timeInVeryLowPercent',
  timeInAnyLowPercent: 'timeInAnyLowPercent',
  dropInTimeInTargetPercent: 'dropInTimeInTargetPercent',
  timeInTargetPercent: 'timeInTargetPercent',
  timeCGMUsePercent: 'timeCGMUsePercent',
  meetingTargets: 'meetingTargets',
  noData: 'noData',
};

const getValidTideDashboardCategories = (categoriesString) => {
  // categoriesString should be a comma-delimited list of category names from launchDarkly

  return categoriesString.split(',').map(category => category.trim()).filter(category => !!CATEGORY[category]);
};

const prefixTideDashboardMetric = metric => `Clinic - Tide Dashboard - ${metric}`;

const editPatient = (patient, setSelectedPatient, selectedClinicId, trackMetric, setShowEditPatientDialog, source) => {
  trackMetric('Clinic - Edit patient', { clinicId: selectedClinicId, source });
  setSelectedPatient(patient);
  setShowEditPatientDialog(true);
};

const editPatientDataConnections = (patient, setSelectedPatient, selectedClinicId, trackMetric, setShowDataConnectionsModal, source) => {
  trackMetric('Clinic - Edit patient data connections', { clinicId: selectedClinicId, source });
  setSelectedPatient(patient);
  setShowDataConnectionsModal(true);
};

const MoreMenu = React.memo(({
  patient,
  selectedClinicId,
  t,
  trackMetric,
  setSelectedPatient,
  setShowDataConnectionsModal,
  setShowEditPatientDialog,
}) => {
  const handleEditPatient = useCallback(() => {
    editPatient(patient, setSelectedPatient, selectedClinicId, trackMetric, setShowEditPatientDialog, 'action menu');
  }, [patient, setSelectedPatient, selectedClinicId, trackMetric, setShowEditPatientDialog]);

  const handleEditPatientDataConnections = useCallback(() => {
    editPatientDataConnections(patient, setSelectedPatient, selectedClinicId, trackMetric, setShowDataConnectionsModal, 'action menu');
  }, [patient, setSelectedPatient, selectedClinicId, trackMetric, setShowDataConnectionsModal]);

  const items = useMemo(() => ([{
    icon: EditIcon,
    iconLabel: t('Edit Patient Information'),
    iconPosition: 'left',
    id: `edit-${patient?.id}`,
    variant: 'actionListItem',
    onClick: (_popupState) => {
      _popupState.close();
      handleEditPatient(patient);
    },
    text: t('Edit Patient Information'),
  }, {
    iconSrc: DataInIcon,
    iconLabel: t('Bring Data into Tidepool'),
    iconPosition: 'left',
    id: `edit-data-connections-${patient.id}`,
    variant: 'actionListItem',
    onClick: (_popupState) => {
      _popupState.close();
      handleEditPatientDataConnections(patient);
    },
    text: t('Bring Data into Tidepool'),
  }]), [
    handleEditPatient,
    patient,
    t,
  ]);

  return <PopoverMenu id={`action-menu-${patient?.id}`} items={items} icon={MoreVertRoundedIcon} />;
});

const SortPopover = React.memo(props => {
  const {
    section,
    sections,
    selectedClinicId,
    setSections,
    trackMetric,
    t,
  } = props;

  const id = `sort-${section.groupKey}`;
  const invertSortLabels = section.groupKey === 'dropInTimeInTargetPercent';

  const sortOptions = [
    { value: invertSortLabels ? 'desc' : 'asc', label: t('Low → High') },
    { value: invertSortLabels ? 'asc' : 'desc', label: t('High → Low') },
  ];

  if (invertSortLabels) sortOptions.reverse();

  const sortPopupFilterState = usePopupState({
    variant: 'popover',
    popupId: `${id}-popup`,
  });

  return (
    <Flex>
      <Box
        onClick={() => {
          if (!sortPopupFilterState.isOpen) trackMetric(prefixTideDashboardMetric('Sort popover opened'), { clinicId: selectedClinicId, section: section.groupKey });
        }}
        sx={{ flexShrink: 0 }}
      >
        <Button
          variant="textSecondary"
          id={`${id}-popup-trigger`}
          selected={sortPopupFilterState.isOpen}
          {...bindTrigger(sortPopupFilterState)}
          icon={KeyboardArrowDownRoundedIcon}
          iconLabel="Update sort order"
          sx={{ fontSize: 0, fontWeight: 'medium', lineHeight: 1.3 }}
        >
          {t('Sort')} {find(sortOptions, { value: section.sortDirection })?.label}
        </Button>
      </Box>

      <Popover
        // minWidth="10em"
        closeIcon
        {...bindPopover(sortPopupFilterState)}
        onClickCloseIcon={() => {
          trackMetric(prefixTideDashboardMetric('Sort popover closed'), {
            clinicId: selectedClinicId,
            section: section.groupKey,
          });
        }}
        onClose={() => {
          sortPopupFilterState.close();
        }}
      >
        <DialogContent px={2} py={3} dividers>
          <RadioGroup
            id={`${id}-options`}
            name={`${id}-options`}
            options={sortOptions}
            variant="vertical"
            fontSize={0}
            value={section.sortDirection}
            onChange={event => {
              trackMetric(prefixTideDashboardMetric('Sort popover updated'), {
                clinicId: selectedClinicId,
                section: section.groupKey,
                sort: event.target.value,
              });

              const updatedSections = map(sections, sectionState => (sectionState.groupKey === section.groupKey
                ? {...sectionState, sortDirection: event.target.value}
                : sectionState
              ));

              setSections(updatedSections);
              sortPopupFilterState.close();
            }}
          />
        </DialogContent>
      </Popover>
    </Flex>
  )
});

const TideDashboardSection = React.memo(props => {
  const {
    api,
    location,
    history,
    clinicBgUnits,
    config,
    dispatch,
    emptyContentNode,
    emptyText,
    patients,
    patientTags,
    section,
    sections,
    selectedClinicId,
    setSections,
    setSelectedPatient,
    setShowDataConnectionsModal,
    setShowEditPatientDialog,
    showTideDashboardLastReviewed,
    showTideDashboardPatientDrawer,
    t,
    trackMetric,
  } = props;

  const statEmptyText = '--';

  const dexcomConnectStateUI = React.useMemo(() => ({
    noPendingConnections: {
      colorPalette: 'neutral',
      icon: null,
      text: t('No Pending Connections'),
    },
    pending: {
      colorPalette: 'info',
      icon: null,
      text: t('Invite Sent'),
    },
    pendingReconnect: {
      colorPalette: 'info',
      icon: null,
      text: t('Invite Sent'),
    },
    pendingExpired: {
      colorPalette: 'warning',
      icon: ErrorRoundedIcon,
      text: t('Invite Expired'),
    },
    connected: {
      colorPalette: 'info',
      icon: null,
      text: t('Connected'),
    },
    disconnected: {
      colorPalette: 'warning',
      icon: ErrorRoundedIcon,
      text: t('Patient Disconnected'),
    },
    error: {
      colorPalette: 'warning',
      icon: ErrorRoundedIcon,
      text: t('Error Connecting'),
    },
    unknown: {
      colorPalette: 'warning',
      icon: ErrorRoundedIcon,
      text: t('Unknown Status'),
    },
  }), []);

  const handleClickPatient = useCallback(patient => {
    return () => {
      trackMetric('Selected PwD');

      const isValidAgpPeriod = ['7d', '14d', '30d'].includes(config?.period);

      if (showTideDashboardPatientDrawer && isValidAgpPeriod) {
        const { search, pathname } = location;
        const params = new URLSearchParams(search);
        params.set('drawerPatientId', patient.id);
        history.replace({ pathname, search: params.toString() });

        return;
      }

      dispatch(push(`/patients/${patient?.id}/data/trends?dashboard=tide`));
    }
  }, [dispatch, trackMetric, showTideDashboardPatientDrawer, config]);

  const handleEditPatientDataConnections = useCallback((patient) => {
    editPatientDataConnections(patient, setSelectedPatient, selectedClinicId, trackMetric, setShowDataConnectionsModal, 'dexcom connection status');
  }, [setSelectedPatient, selectedClinicId, trackMetric, setShowDataConnectionsModal]);

  const renderPatientName = useCallback(({ patient }) => (
    <Box onClick={handleClickPatient(patient)} sx={{ cursor: 'pointer' }}>
      <Text
        sx={{
          display: 'inline-block',
          fontSize: [1, null, 0],
          fontWeight: 'medium',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          width: '100%',
        }}
      >
        {patient?.fullName}
      </Text>
    </Box>
  ), [handleClickPatient]);

  const renderAverageGlucose = useCallback(summary => {
    const averageGlucose = summary?.averageGlucoseMmol;
    const bgPrefs = { bgUnits: clinicBgUnits };

    const formattedAverageGlucose = clinicBgUnits === MMOLL_UNITS
      ? formatBgValue(averageGlucose, bgPrefs)
      : formatBgValue(utils.translateBg(averageGlucose, clinicBgUnits), bgPrefs);

    return averageGlucose ? (
      <Box className="patient-average-glucose">
        <Text sx={{ fontWeight: 'medium' }}>{formattedAverageGlucose}</Text>
      </Box>
    ) : null;
  }, [clinicBgUnits]);

  const renderGMI = useCallback(summary => {
    const cgmUsePercent = (summary?.timeCGMUsePercent || 0);
    const cgmHours = (summary?.timeCGMUseMinutes || 0) / 60;
    const gmi = summary?.glucoseManagementIndicator;
    const minCgmHours = 24;
    const minCgmPercent = 0.7;
    let formattedGMI = gmi ? utils.formatDecimal(gmi, 1) : statEmptyText;

    if (includes(['1d', '7d'], config?.period)
      || cgmUsePercent < minCgmPercent
      || cgmHours < minCgmHours
    ) formattedGMI = statEmptyText;

    return (
      <Box classname="patient-gmi">
        <Text sx={{ fontWeight: 'medium' }}>{formattedGMI}</Text>
        {formattedGMI !== statEmptyText && <Text sx={{ fontSize: '10px' }}> %</Text>}
      </Box>
    );
  }, [config?.period]);

  const renderTimeInPercent = useCallback((summaryKey, summary) => {

    const rawValue = (summary?.[summaryKey]);

    let formattedValue = isFinite(rawValue)
      ? formatStatsPercentage(rawValue)
      : statEmptyText;

    return (
      <Box classname={`patient-${summaryKey}`}>
        <Text sx={{ fontWeight: 'medium' }}>{formattedValue}</Text>
        {formattedValue !== statEmptyText && <Text sx={{ fontSize: '10px' }}> %</Text>}
      </Box>
    );
  }, []);

  const renderPatientTags = useCallback(({ patient }) => {
    const filteredPatientTags = reject(patient?.tags || [], tagId => !patientTags[tagId]);

    return (
      <TagList
          maxTagsVisible={4}
          maxCharactersVisible={12}
          popupId={`tags-overflow-${patient?.id}`}
          tagProps={{ variant: 'compact' }}
          tags={map(filteredPatientTags, tagId => patientTags?.[tagId])}
      />
    );
  }, [patientTags]);

  const renderLastReviewed = useCallback(({ patient }) => {
    return <PatientLastReviewed api={api} trackMetric={trackMetric} metricSource="TIDE dashboard" patientId={patient.id} recentlyReviewedThresholdDate={moment().startOf('isoWeek').toISOString()} />
  }, [api, trackMetric]);

  const renderBgRangeSummary = useCallback(summary => {
    // Alternate glycemic ranges not applied in TIDE Dashboard for now
    const glycemicRanges = GLYCEMIC_RANGE.ADA_STANDARD;

    return <BgSummaryCell
    id={summary.patient.id}
    summary={summary}
    config={config}
    clinicBgUnits={clinicBgUnits}
    glycemicRanges={glycemicRanges}
    activeSummaryPeriod={config?.period}
  />
  }, [clinicBgUnits, config]);

  const renderTimeInTargetPercentDelta = useCallback(summary => {
    const timeInTargetPercentDelta = (summary?.timeInTargetPercentDelta);

    return timeInTargetPercentDelta ? (
      <DeltaBar
        sx={{ fontWeight: 'medium' }}
        delta={timeInTargetPercentDelta * 100}
        max={30}
      />
    ) : (
      <Text sx={{ fontWeight: 'medium' }}>{statEmptyText}</Text>
    );
  }, []);

  const renderMore = useCallback(({ patient }) => {
    return <MoreMenu
      patient={patient}
      selectedClinicId={selectedClinicId}
      t={t}
      trackMetric={trackMetric}
      setSelectedPatient={setSelectedPatient}
      setShowDataConnectionsModal={setShowDataConnectionsModal}
      setShowEditPatientDialog={setShowEditPatientDialog}
      prefixTideDashboardMetric={prefixTideDashboardMetric}
    />;
  }, [
    selectedClinicId,
    t,
    trackMetric,
    setSelectedPatient,
    setShowEditPatientDialog,
  ]);

  const renderDexcomConnectionStatus = useCallback(({ patient }) => {
    const dexcomDataSource = find(patient?.dataSources, { providerName: 'dexcom' });
    const dexcomAuthInviteExpired = dexcomDataSource?.expirationTime < moment.utc().toISOString();
    let dexcomConnectState;

    if (dexcomDataSource) {
      dexcomConnectState = includes(keys(dexcomConnectStateUI), dexcomDataSource?.state)
        ? dexcomDataSource.state
        : 'unknown';

      if (includes(['pending', 'pendingReconnect'], dexcomConnectState) && dexcomAuthInviteExpired) dexcomConnectState = 'pendingExpired';
    } else {
      dexcomConnectState = 'noPendingConnections';
    }

    const showViewButton = includes([
      'disconnected',
      'error',
      'noPendingConnections',
      'pendingExpired',
      'unknown',
    ], dexcomConnectState);

    const StatusBadge = () => (
      <Pill
        className="patient-dexcom-connection-status"
        icon={dexcomConnectStateUI[dexcomConnectState].icon}
        text={dexcomConnectStateUI[dexcomConnectState].text}
        label={t('dexcom connection stauts')}
        colorPalette={dexcomConnectStateUI[dexcomConnectState].colorPalette}
        condensed
      />
    );

    return dexcomConnectState ? (
      <>
      {showViewButton ? (
        <HoverButton
          buttonText={t('View')}
          buttonProps={{
            onClick: () => handleEditPatientDataConnections(patient),
            variant: 'textSecondary',
            ml: -2,
            sx: {
              fontSize: 0,
              fontWeight: fontWeights.medium,
              textDecoration: 'underline',
              color: colors.purpleMedium,
              ':hover': {
                color: colors.purpleMedium,
                textDecoration: 'underline',
              }
            }
          }}
        >
          <Box sx={{ whiteSpace: 'nowrap' }}>
            <StatusBadge />
          </Box>
        </HoverButton>
      ) : <StatusBadge />
    }
      </>
    ) : null;
  }, [dexcomConnectStateUI, t]);

  const renderDaysSinceLastData = useCallback(({ daysSinceLastData }) => {
    return daysSinceLastData ? (
      <Box className="patient-dexcom-connection-status">
        <Text sx={{ fontWeight: 'medium' }}>{daysSinceLastData}</Text>
      </Box>
    ) : (
      <Text sx={{ fontWeight: 'medium' }}>-</Text>
    );
  }, []);

  const veryLowGlucoseThreshold = clinicBgUnits === MGDL_UNITS
    ? utils.translateBg(config?.veryLowGlucoseThreshold, MGDL_UNITS)
    : utils.formatDecimal(config?.veryLowGlucoseThreshold, 1);

  const lowGlucoseThreshold = clinicBgUnits === MGDL_UNITS
    ? utils.translateBg(config?.lowGlucoseThreshold, MGDL_UNITS)
    : utils.formatDecimal(config?.lowGlucoseThreshold, 1);

  const highGlucoseThreshold = clinicBgUnits === MGDL_UNITS
    ? utils.translateBg(config?.highGlucoseThreshold, MGDL_UNITS)
    : utils.formatDecimal(config?.highGlucoseThreshold, 1);

  const columns = useMemo(() => {
    const cols = [
      {
        title: t('Patient Name'),
        field: 'patient.fullName',
        align: 'left',
        render: renderPatientName,
        width: 160,
      },
      {
        title: t('Avg. Glucose'),
        field: 'averageGlucoseMmol',
        align: 'center',
        render: renderAverageGlucose,
      },
      {
        title: t('GMI'),
        field: 'glucoseManagementIndicator',
        align: 'center',
        render: renderGMI,
      },
      {
        title: t('CGM Use'),
        field: 'timeCGMUsePercent',
        align: 'center',
        render: renderTimeInPercent.bind(null, 'timeCGMUsePercent'),
      },
      {
        title: t('% Time < {{threshold}}', { threshold: veryLowGlucoseThreshold }),
        field: 'timeInVeryLowPercent',
        align: 'center',
        render: renderTimeInPercent.bind(null, 'timeInVeryLowPercent'),
      },
      {
        title: t('% Time < {{upper}}', { upper: lowGlucoseThreshold }),
        field: 'timeInLowPercent',
        align: 'center',
        render: renderTimeInPercent.bind(null, 'timeInAnyLowPercent'),
      },
      {
        title: t('% TIR {{lower}}-{{upper}}', { lower: lowGlucoseThreshold, upper: highGlucoseThreshold }),
        field: 'timeInTargetPercent',
        align: 'center',
        render: renderTimeInPercent.bind(null, 'timeInTargetPercent'),
      },
      {
        title: t('% Time in Range'),
        field: 'bgRangeSummary',
        align: 'center',
        render: renderBgRangeSummary,
        width: 207,
      },
      {
        title: t('% Change in TIR'),
        field: 'timeInTargetPercentDelta',
        align: 'center',
        render: renderTimeInTargetPercentDelta,
        width: 140,
      },
      {
        title: t('Tags'),
        field: 'patient.tags',
        align: 'left',
        render: renderPatientTags,
        width: 170,
      },
      {
        title: '',
        field: 'more',
        render: renderMore,
        align: 'right',
        width: 28,
        className: 'action-menu no-padding',
      },
    ];

    if (showTideDashboardLastReviewed) {
      cols.splice(10, 0, {
        title: t('Last Reviewed'),
        field: 'lastReviewed',
        align: 'left',
        render: renderLastReviewed,
        width: 140,
      })
    }

    if (section.groupKey === 'noData') {
      cols.splice(1, 8, ...[
        {
          title: t('Dexcom Connection Status'),
          field: 'patient.dataSources',
          align: 'left',
          render: renderDexcomConnectionStatus,
        },
        {
          title: t('Days Since Last Data '),
          field: 'lastData',
          align: 'center',
          render: renderDaysSinceLastData,
        },
        {
          field: 'spacer',
          className: 'group-spacer',
        },
      ]);
    }

    return cols;
  }, [
    highGlucoseThreshold,
    lowGlucoseThreshold,
    renderAverageGlucose,
    renderBgRangeSummary,
    renderGMI,
    renderLastReviewed,
    renderMore,
    renderPatientName,
    renderPatientTags,
    renderTimeInPercent,
    renderTimeInTargetPercentDelta,
    showTideDashboardLastReviewed,
    showTideDashboardPatientDrawer,
    t,
    veryLowGlucoseThreshold,
  ]);

  const sectionLabelsMap = {
    timeInVeryLowPercent: t('Time below {{veryLowGlucoseThreshold}} {{clinicBgUnits}} > 1%', {
      veryLowGlucoseThreshold,
      clinicBgUnits,
    }),
    timeInAnyLowPercent: t('Time below {{lowGlucoseThreshold}} {{clinicBgUnits}} > 4%', {
      lowGlucoseThreshold,
      clinicBgUnits,
    }),
    dropInTimeInTargetPercent: t('Drop in Time in Range > 15%'),
    timeInTargetPercent: t('Time in Range < 70%'),
    timeCGMUsePercent: t('CGM Wear Time < 70%'),
    meetingTargets: t('Meeting Targets'),
    noData: t('Data Issues'),
  };

  return (
    <Box className='dashboard-section' id={`dashboard-section-${section.groupKey}`} mb={4}>
      <Flex sx={{ justifyContent: 'space-between', alignItems: 'center' }}>
        <Text
          className='dashboard-section-label'
          sx={{
            color: 'purples.9',
            fontSize: 1,
            fontWeight: 'medium',
          }}
          mb={2}
        >
          {sectionLabelsMap[section.groupKey]}
        </Text>

        {/* Commenting out sort functionality for now */}
        {/* <SortPopover
          section={section}
          sections={sections}
          selectedClinicId={selectedClinicId}
          setSections={setSections}
          trackMetric={trackMetric}
          t={t}
        /> */}
      </Flex>

      <Table
        className='dashboard-table'
        id={`dashboard-table-${section.groupKey}`}
        variant="tableGroup"
        label={'peopletablelabel'}
        columns={columns}
        data={patients}
        sx={{ fontSize: 0 }}
        order={section.sortDirection}
        orderBy={section.sortKey}
        emptyContentNode={emptyContentNode}
        emptyText={emptyText}
        containerProps={{
          sx: {
            '.table-empty-text': {
              backgroundColor: 'white',
              borderBottomLeftRadius: radii.medium,
              borderBottomRightRadius: radii.medium,
            },
          }
        }}
      />
    </Box>
  );
}, ((prevProps, nextProps) => (
  prevProps.section.sortDirection === nextProps.section.sortDirection &&
  prevProps.config === nextProps.config &&
  prevProps.patients === nextProps.patients &&
  prevProps.showTideDashboardLastReviewed === nextProps.showTideDashboardLastReviewed &&
  prevProps.showTideDashboardPatientDrawer === nextProps.showTideDashboardPatientDrawer
)));

export const TideDashboard = (props) => {
  const { t, api, trackMetric } = props;
  const isFirstRender = useIsFirstRender();
  const dispatch = useDispatch();
  const { set: setToast } = useToasts();
  const selectedClinicId = useSelector((state) => state.blip.selectedClinicId);
  const loggedInUserId = useSelector((state) => state.blip.loggedInUserId);
  const pdf = useSelector((state) => state.blip.pdf);
  const currentPatientInViewId = useSelector((state) => state.blip.currentPatientInViewId);
  const clinic = useSelector(state => state.blip.clinics?.[selectedClinicId]);
  const mrnSettings = clinic?.mrnSettings ?? {};
  const { config, results: patientGroups } = useSelector((state) => state.blip.tideDashboardPatients);
  const timePrefs = useSelector((state) => state.blip.timePrefs);
  const location = useLocation();
  const history = useHistory();
  const [showTideDashboardConfigDialog, setShowTideDashboardConfigDialog] = useState(false);
  const [showDataConnectionsModal, setShowDataConnectionsModal] = useState(false);
  const [showEditPatientDialog, setShowEditPatientDialog] = useState(false);
  const [selectedPatient, setSelectedPatient] = useState(null);
  const [loading, setLoading] = useState(false);
  const [patientFormContext, setPatientFormContext] = useState();
  const [tideDashboardFormContext, setTideDashboardFormContext] = useState();
  const [clinicBgUnits, setClinicBgUnits] = useState(clinic?.preferredBgUnits || MGDL_UNITS);
  const [localConfig] = useLocalStorage('tideDashboardConfig', {});
  const localConfigKey = [loggedInUserId, selectedClinicId].join('|');
  const patientTags = useMemo(() => keyBy(clinic?.patientTags, 'id'), [clinic?.patientTags]);
  const {
    showTideDashboard,
    showTideDashboardLastReviewed,
    showTideDashboardPatientDrawer,
    // tideDashboardCategories,
  } = useFlags();
  const ldClient = useLDClient();
  const ldContext = ldClient.getContext();

  const tideDashboardCategories = 'meetingTargets, blahr, timeInAnyLowPercent';

  const existingMRNs = useSelector(state => state.blip.clinicMRNsForPatientFormValidation)?.filter(mrn => mrn !== selectedPatient?.mrn) || [];

  const {
    fetchingPatientFromClinic,
    updatingClinicPatient,
    fetchingTideDashboardPatients,
  } = useSelector((state) => state.blip.working);

  const previousUpdatingClinicPatient = usePrevious(updatingClinicPatient);
  const previousFetchingTideDashboardPatients = usePrevious(fetchingTideDashboardPatients);

  const sectionOptions = [
    { groupKey: CATEGORY.timeInVeryLowPercent, sortDirection: 'desc', sortKey: 'timeInVeryLowPercent' },
    { groupKey: CATEGORY.timeInAnyLowPercent, sortDirection: 'desc', sortKey: 'timeInAnyLowPercent' },
    { groupKey: CATEGORY.dropInTimeInTargetPercent, sortDirection: 'asc', sortKey: 'timeInTargetPercentDelta' },
    { groupKey: CATEGORY.timeInTargetPercent, sortDirection: 'asc', sortKey: 'timeInTargetPercent' },
    { groupKey: CATEGORY.timeCGMUsePercent, sortDirection: 'asc', sortKey: 'timeCGMUsePercent' },
    { groupKey: CATEGORY.meetingTargets, sortDirection: 'desc', sortKey: 'timeInVeryLowPercent' },
    { groupKey: CATEGORY.noData, sortDirection: 'desc', sortKey: 'daysSinceLastData' },
  ];

  // Display only sections that are
  const displayedCategories = getValidTideDashboardCategories(tideDashboardCategories || '');
  const sections = displayedCategories.map(category => (sectionOptions.find(({ groupKey }) => groupKey === category)));

  function handleCloseOverlays() {
    setShowTideDashboardConfigDialog(false);
    setShowDataConnectionsModal(false);
    setShowEditPatientDialog(false);

    setTimeout(() => {
      setPatientFormContext(null);
      setSelectedPatient(null);
    });
  }

  const handleAsyncResult = useCallback((workingState, successMessage, onComplete = handleCloseOverlays) => {
    const { inProgress, completed, notification, prevInProgress } = workingState;

    if (!isFirstRender && !inProgress && prevInProgress !== false) {
      if (completed) {
        onComplete();
        successMessage && setToast({
          message: successMessage,
          variant: 'success',
        });
      }

      if (completed === false) {
        setToast({
          message: get(notification, 'message'),
          variant: 'danger',
        });
      }

      setLoading(false);
    }
  }, [isFirstRender, setToast]);

  const handlePatientEdited = useCallback(() => {
    if (patientFormContext?.status?.showDataConnectionsModalNext) {
      setShowEditPatientDialog(false);
      editPatientDataConnections(selectedPatient, setSelectedPatient, selectedClinicId, trackMetric, setShowDataConnectionsModal, 'Tide dashboard - patient modal');
    } else {
      handleCloseOverlays();
    }
  }, [handleCloseOverlays, patientFormContext?.status]);

  useEffect(() => {
    // Only process detected updates if patient edit form is showing. Other child components, such as
    // the PatientEmailModal, may also update the patient, and handle the results
    if (showEditPatientDialog) {
      handleAsyncResult({ ...updatingClinicPatient, prevInProgress: previousUpdatingClinicPatient?.inProgress }, t('You have successfully updated a patient.'), handlePatientEdited)
    }
  }, [
    handleAsyncResult,
    handlePatientEdited,
    t,
    updatingClinicPatient,
    patientFormContext?.status,
    previousUpdatingClinicPatient?.inProgress,
    showEditPatientDialog,
  ]);

  // Provide latest patient state for the edit form upon fetch
  useEffect(() => {
    const fetchedClinicPatient = clinic?.patients?.[selectedPatient?.id];
    if (fetchingPatientFromClinic.completed && selectedPatient?.id && fetchedClinicPatient) {
      setSelectedPatient(fetchedClinicPatient);
    }
  }, [fetchingPatientFromClinic, selectedPatient?.id, clinic?.patients]);

  const fetchDashboardPatients = useCallback((categories, config) => {
    const options = { ...(config || localConfig?.[localConfigKey]) };
    if (options) {
      const lastData = Number(options.lastData);
      const queryOptions = { period: options.period, lastData };

      queryOptions['tags'] = reject(options.tags || [], tagId => !patientTags?.[tagId]);
      queryOptions['lastDataCutoff'] = moment(getLocalizedCeiling(new Date().toISOString(), timePrefs)).subtract(lastData, 'days').toISOString();

      if (!!categories) queryOptions['categories'] = categories;

      setLoading(true);
      dispatch(actions.async.fetchTideDashboardPatients(api, selectedClinicId, queryOptions));
    }
  }, [api, dispatch, localConfig, localConfigKey, selectedClinicId]);

  useEffect(() => {
    dispatch(actions.worker.dataWorkerRemoveDataRequest(null, currentPatientInViewId));
    dispatch(actions.sync.clearPatientInView());
    setClinicBgUnits((clinic?.preferredBgUnits || MGDL_UNITS));
  }, [clinic]);

  useEffect(() => {
    if (clinic) {
      setClinicBgUnits((clinic.preferredBgUnits || MGDL_UNITS));
    }
  }, [
    clinic,
    ldContext,
    ldClient,
    dispatch,
    localConfig,
    localConfigKey,
    showTideDashboard,
    fetchDashboardPatients,
  ]);

  useEffect(() => {
    // Redirect to the workspace if the LD clinic context is set and showTideDashboard flag is false
    // and the clinic does not have the tideDashboard entitlement
    if ((clinic?.entitlements && !clinic.entitlements.tideDashboard) && (ldContext?.clinic?.tier && !showTideDashboard)) dispatch(push('/clinic-workspace'));
  }, [ldContext, showTideDashboard, selectedClinicId, clinic?.entitlements, dispatch]);

  useEffect(() => {
    handleAsyncResult({ ...fetchingTideDashboardPatients, prevInProgress: previousFetchingTideDashboardPatients?.inProgress }, null, handleCloseOverlays);
  }, [fetchingTideDashboardPatients, handleAsyncResult, previousFetchingTideDashboardPatients?.inProgress]);

  function handlePatientFormChange(formikContext) {
    setPatientFormContext({...formikContext});
  }

  useEffect(() => {
    if (isNil(tideDashboardCategories)) return;

    const categories = getValidTideDashboardCategories(tideDashboardCategories || '');

    if (validateTideConfig(localConfig?.[localConfigKey], patientTags)) {
      fetchDashboardPatients(categories);
    } else {
      setShowTideDashboardConfigDialog(true);
    }

    // Always clear stored dashboard results upon unmount to avoid flashing stale results upon remount
    return () => {
      dispatch(actions.sync.clearTideDashboardPatients());
    }
  }, [showTideDashboard, tideDashboardCategories]);

  const drawerPatientId = new URLSearchParams(location.search).get('drawerPatientId') || null;

  // Patient Drawer effects
  useEffect(() => {
    // If invalid period for AGP (ie. 1 day), clear drawerPatientId from searchParams
    if (!!drawerPatientId && !!config?.period && !isValidAgpPeriod(config.period)) {
      const { search, pathname } = location;

      const params = new URLSearchParams(search);
      params.delete('drawerPatientId');
      history.replace({ pathname, search: params.toString() });
    }

    // Failsafe to ensure blip.pdf is always cleared out after drawer is closed
    if (!drawerPatientId && !isEmpty(pdf)) {
      dispatch(actions.worker.removeGeneratedPDFS());
      dispatch(actions.worker.dataWorkerRemoveDataRequest(null, drawerPatientId));
    }
  }, [drawerPatientId, pdf, config, location, history]);

  const handleEditPatientConfirm = useCallback(() => {
    trackMetric('Clinic - Edit patient confirmed', { clinicId: selectedClinicId });
    const updatedTags = [...(patientFormContext?.values?.tags || [])];
    const existingTags = [...(selectedPatient?.tags || [])];

    if (!isEqual(updatedTags.sort(), existingTags.sort())) {
      trackMetric(prefixTideDashboardMetric('Edit patient tags confirm'), { clinicId: selectedClinicId });
    }
    patientFormContext?.handleSubmit();
  }, [patientFormContext, selectedClinicId, trackMetric, selectedPatient?.tags]);

  const handleEditPatientAndAddDataSourcesConfirm = useCallback(() => {
    trackMetric('Clinic - Edit patient next', { clinicId: selectedClinicId, source: 'Tide dashboard' });
    patientFormContext?.setStatus({ showDataConnectionsModalNext: true });
    handleEditPatientConfirm();
  }, [patientFormContext, selectedClinicId, trackMetric, handleEditPatientConfirm]);

  const handleClosePatientDrawer = useCallback(() => {
    const { search, pathname } = location;

    const params = new URLSearchParams(search);
    params.delete('drawerPatientId');
    history.replace({ pathname, search: params.toString() });
  });

  function handleConfigureTideDashboard() {
    trackMetric('Clinic - Show Tide Dashboard config dialog', { clinicId: selectedClinicId, source: 'Tide dashboard' });
    setShowTideDashboardConfigDialog(true);
  }

  const handleConfigureTideDashboardConfirm = useCallback(() => {
    trackMetric('Clinic - Show Tide Dashboard config dialog confirmed', { clinicId: selectedClinicId, source: 'Tide dashboard' });
    tideDashboardFormContext?.handleSubmit();
    fetchDashboardPatients(tideDashboardCategories, tideDashboardFormContext?.values);
  }, [fetchDashboardPatients, tideDashboardCategories, tideDashboardFormContext, selectedClinicId, trackMetric]);

  function handleTideDashboardConfigFormChange(formikContext) {
    setTideDashboardFormContext({...formikContext});
  }

  const renderHeader = () => {
    const periodDaysText = keyBy(summaryPeriodOptions, 'value')?.[config?.period]?.label
    const lastDataDaysText = keyBy(lastDataFilterOptions, 'value')?.[config?.lastData]?.label

    return (
      <Flex
        mb={3}
        sx={{ rowGap: 2, columnGap: 3, justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap' }}
      >
        <Flex sx={{ gap: 3, alignItems: 'center' }}>
          <Title id="tide-dashboard-header" sx={{ fontWeight: 'medium', fontSize: '18px' }}>{t('TIDE Dashboard')}</Title>

          <Flex sx={{ gap: 2, position: 'relative', top: '2px', alignItems: 'center' }}>
            <Text
              sx={{
                fontSize: 0,
                fontWeight: 'medium',
                color: 'text.primaryGrey',
              }}
              ml={2}
            >
              {t('Summarizing')}
            </Text>

            <Text
              id="tide-dashboard-summary-period"
              as={Flex}
              px={3}
              sx={{
                borderRadius: radii.medium,
                alignContent: 'center',
                flexWrap: 'wrap',
                fontSize: 0,
                fontWeight: 'medium',
                height: '24px',
                bg: 'white',
                color: loading ? 'white' : 'text.primary',
              }}
            >
              {periodDaysText} {t('of data')}
            </Text>
          </Flex>

          <Flex sx={{ gap: 2, position: 'relative', top: '2px', alignItems: 'center' }}>
            <Text
              sx={{
                fontSize: 0,
                fontWeight: 'medium',
                color: 'text.primaryGrey',
              }}
              ml={2}
            >
              {t('Data recency')}
            </Text>

            <Text
              id="tide-dashboard-last-data"
              as={Flex}
              px={3}
              sx={{
                borderRadius: radii.medium,
                alignContent: 'center',
                flexWrap: 'wrap',
                fontSize: 0,
                fontWeight: 'medium',
                height: '24px',
                bg: 'white',
                color: loading ? 'white' : 'text.primary',
              }}
            >
              {lastDataDaysText}
            </Text>
          </Flex>
        </Flex>

        <Button
          id="update-dashboard-config"
          variant="filter"
          icon={KeyboardArrowDownRoundedIcon}
          iconLabel="Open dashboard config"
          onClick={handleConfigureTideDashboard}
          px={3}
          sx={{ fontSize: 1, lineHeight: 5, border: 'none' }}
        >
          {t('Filter Patients')}
        </Button>
      </Flex>
    )
  };

  const renderTideDashboardConfigDialog = useCallback(() => {
    return (
      <Dialog
        id="tideDashboardConfig"
        aria-labelledby="dialog-title"
        open={showTideDashboardConfigDialog}
        onClose={handleCloseOverlays}
        maxWidth="sm"
      >
        <DialogTitle sx={{ alignItems: 'flex-start' }} onClose={handleCloseOverlays}>
          <Box mr={2}>
            <MediumTitle sx={{ fontSize: 2 }} id="dialog-title">{t('Select Patients to Display in the TIDE Dashboard')}</MediumTitle>
            <Body1 sx={{ fontWeight: 'medium', color: 'grays.4' }}>{t('You must make a selection in each category')}</Body1>
          </Box>
        </DialogTitle>

        <DialogContent>
          <TideDashboardConfigForm api={api} trackMetric={trackMetric} onFormChange={handleTideDashboardConfigFormChange} />
        </DialogContent>

        <DialogActions>
          <Button
            id="configureTideDashboardConfirm"
            variant="primary"
            onClick={handleConfigureTideDashboardConfirm}
            processing={fetchingTideDashboardPatients.inProgress}
            disabled={!fieldsAreValid(keys(tideDashboardFormContext?.values), tideDashboardConfigSchema, tideDashboardFormContext?.values)}
          >
            {t('Apply')}
          </Button>
        </DialogActions>
      </Dialog>
    );
  }, [
    api,
    fetchingTideDashboardPatients.inProgress,
    handleConfigureTideDashboardConfirm,
    tideDashboardFormContext?.values,
    showTideDashboardConfigDialog,
    t,
    trackMetric
  ]);

  const renderEditPatientDialog = useCallback(() => {
    return (
      <Dialog
        id="editPatient"
        aria-labelledby="dialog-title"
        open={showEditPatientDialog}
        onClose={handleCloseOverlays}
      >
        <DialogTitle onClose={() => {
          trackMetric('Clinic - Edit patient close', { clinicId: selectedClinicId });
          handleCloseOverlays()
        }}>
          <MediumTitle id="dialog-title">{t('Edit Patient Details')}</MediumTitle>
        </DialogTitle>

        <DialogContent>
          <PatientForm api={api} trackMetric={trackMetric} onFormChange={handlePatientFormChange} patient={selectedPatient} action="edit" />
        </DialogContent>

        <DialogActions>
          <Button id="editPatientCancel" variant="secondary" onClick={() => {
            trackMetric('Clinic - Edit patient cancel', { clinicId: selectedClinicId, source: 'TIDE dashboard' });
            handleCloseOverlays();
          }}>
            {t('Cancel')}
          </Button>

          <Button
            id="editPatientNext"
            variant="secondary"
            onClick={handleEditPatientAndAddDataSourcesConfirm}
            processing={updatingClinicPatient.inProgress && patientFormContext?.status?.showDataConnectionsModalNext}
            disabled={!fieldsAreValid(keys(patientFormContext?.values), validationSchema({mrnSettings, existingMRNs}), patientFormContext?.values)}
          >
            {t('Save & Next')}
          </Button>

          <Button
            id="editPatientConfirm"
            variant="primary"
            onClick={handleEditPatientConfirm}
            processing={updatingClinicPatient.inProgress && !patientFormContext?.status?.showDataConnectionsModalNext}
            disabled={!fieldsAreValid(keys(patientFormContext?.values), validationSchema({mrnSettings, existingMRNs}), patientFormContext?.values)}
          >
            {t('Save Changes')}
          </Button>
        </DialogActions>
      </Dialog>
    );
  }, [
    api,
    existingMRNs,
    handleEditPatientConfirm,
    handleEditPatientAndAddDataSourcesConfirm,
    mrnSettings,
    patientFormContext,
    selectedClinicId,
    selectedPatient,
    showEditPatientDialog,
    t,
    trackMetric,
    updatingClinicPatient.inProgress
  ]);

  const renderDataConnectionsModal = useCallback(() => {
    return (
      <DataConnectionsModal
        open
        patient={selectedPatient}
        onClose={handleCloseOverlays}
        onBack={patientFormContext?.status?.showDataConnectionsModalNext ? () => {
          setShowDataConnectionsModal(false)
          setShowEditPatientDialog(true)
        } : undefined}
      />
    );
  }, [
    handleCloseOverlays,
    patientFormContext?.status,
    selectedPatient,
  ]);

  const renderPatientGroups = useCallback(() => {
    const sectionProps = {
      api,
      location,
      history,
      clinicBgUnits,
      config,
      dispatch,
      patientTags,
      sections,
      selectedClinicId,
      setSelectedPatient,
      setShowDataConnectionsModal,
      setShowEditPatientDialog,
      showTideDashboardLastReviewed,
      showTideDashboardPatientDrawer,
      t,
      trackMetric,
    };

    const hasResults = flatten(values(patientGroups)).length > 0;

    const handleClickClinicWorkspace = () => {
      trackMetric('Clinic - View patient list', {
        clinicId: selectedClinicId,
        source: 'Empty Dashboard Results',
      });

      dispatch(push('/clinic-workspace/patients'));
    };

    each(patientGroups.noData, record => {
      record.daysSinceLastData = moment.utc().diff(record.lastData, 'days');
    })

    return hasResults ? (
      <Box id="tide-dashboard-patient-groups">
        {map(sections, section => (
          <TideDashboardSection
            key={section.groupKey}
            section={section}
            patients={patientGroups[section.groupKey]}
            emptyText={t('There are no patients that match your filter criteria.')}
            {...sectionProps}
          />
        ))}
      </Box>
    ) : (
      <TideDashboardSection
        {...sectionProps}
        section={{}}
        patients={[]}
        emptyContentNode={(
          <Box id="no-tide-results" px={3} py={8} variant="containers.fluidRounded" sx={{ fontSize: 1, color: 'text.primary', textAlign: 'center', a: { color: 'text.link', cursor: 'pointer' } }}>
            <Text mb={3} sx={{ display: 'inline-block', fontWeight: 'bold' }}>
              {t('There are no patients that match your filter criteria.')}
            </Text>

            <Trans i18nKey='html.empty-tide-dashboard-instructions'>
              To make sure your patients are tagged and you have set the correct patient filters, go to your <a className="empty-tide-workspace-link" onClick={handleClickClinicWorkspace}>Clinic Workspace</a>.
            </Trans>
          </Box>
        )}
      />
    );
  }, [
    clinicBgUnits,
    config,
    dispatch,
    patientGroups,
    patientTags,
    sections,
    selectedClinicId,
    setSelectedPatient,
    setShowEditPatientDialog,
    showTideDashboardLastReviewed,
    showTideDashboardPatientDrawer,
    t,
    trackMetric,
  ]);

  return (
    <Box
      id="tide-dashboard"
      sx={{
        alignItems: 'center',
        bg: 'transparent',
        minHeight: '80vh',
      }}
      variant="containers.large"
      mb={8}
      px={3}
    >
      <Loader show={loading} overlay={!!patientGroups} />
      {renderHeader()}
      {patientGroups && renderPatientGroups()}
      {showTideDashboardConfigDialog && renderTideDashboardConfigDialog()}
      {showEditPatientDialog && renderEditPatientDialog()}
      {showDataConnectionsModal && renderDataConnectionsModal()}

      <PatientDrawer
        patientId={drawerPatientId}
        onClose={handleClosePatientDrawer}
        api={api}
        trackMetric={trackMetric}
        period={config?.period}
      />

      <StyledScrollToTop
        smooth
        top={600}
        component={<ArrowUpwardIcon />}
      />
    </Box>
  );
};

TideDashboard.propTypes = {
  api: PropTypes.object.isRequired,
  trackMetric: PropTypes.func.isRequired,
  searchDebounceMs: PropTypes.number.isRequired,
};

TideDashboard.defaultProps = {
  searchDebounceMs: 1000,
};

export default withTranslation()(TideDashboard);
